{"pagination": {"ListInputs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Inputs"}, "ListChannels": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Channels"}, "ListInputSecurityGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "InputSecurityGroups"}, "ListOfferings": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Offerings"}, "ListReservations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Reservations"}, "DescribeSchedule": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ScheduleActions"}, "ListMultiplexPrograms": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "MultiplexPrograms"}, "ListMultiplexes": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Multiplexes"}, "ListInputDevices": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "InputDevices"}, "ListInputDeviceTransfers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "InputDeviceTransfers"}, "ListSignalMaps": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "SignalMaps"}, "ListCloudWatchAlarmTemplates": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "CloudWatchAlarmTemplates"}, "ListCloudWatchAlarmTemplateGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "CloudWatchAlarmTemplateGroups"}, "ListEventBridgeRuleTemplates": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "EventBridgeRuleTemplates"}, "ListEventBridgeRuleTemplateGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "EventBridgeRuleTemplateGroups"}, "ListNodes": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Nodes"}, "ListClusters": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Clusters"}, "ListChannelPlacementGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ChannelPlacementGroups"}, "ListNetworks": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Networks"}, "ListSdiSources": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "SdiSources"}}}