{"pagination": {"ListDataIntegrationFlows": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "flows"}, "ListDataLakeDatasets": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "datasets"}, "ListInstances": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "instances"}, "ListDataIntegrationEvents": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "events"}, "ListDataIntegrationFlowExecutions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "flowExecutions"}, "ListDataLakeNamespaces": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "namespaces"}}}