{"pagination": {"ListNamespaces": {"input_token": "continuationToken", "output_token": "continuationToken", "limit_key": "maxNamespaces", "result_key": "namespaces"}, "ListTableBuckets": {"input_token": "continuationToken", "output_token": "continuationToken", "limit_key": "maxBuckets", "result_key": "tableBuckets"}, "ListTables": {"input_token": "continuationToken", "output_token": "continuationToken", "limit_key": "maxTables", "result_key": "tables"}}}