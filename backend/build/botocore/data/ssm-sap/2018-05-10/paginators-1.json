{"pagination": {"ListApplications": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Applications"}, "ListComponents": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Components"}, "ListDatabases": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Databases"}, "ListOperations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Operations"}, "ListOperationEvents": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "OperationEvents"}, "ListConfigurationCheckDefinitions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ConfigurationChecks"}, "ListConfigurationCheckOperations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ConfigurationCheckOperations"}, "ListSubCheckResults": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "SubCheckResults"}, "ListSubCheckRuleResults": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "RuleResults"}}}