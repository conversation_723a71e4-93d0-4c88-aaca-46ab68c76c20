from flask import Flask, jsonify, request, make_response, redirect
from flask_cors import CORS
import os
from dotenv import load_dotenv
from flask_jwt_extended import J<PERSON>TMana<PERSON>, create_access_token, jwt_required, get_jwt_identity, set_access_cookies, create_refresh_token, set_refresh_cookies, unset_jwt_cookies
from authlib.integrations.flask_client import OAuth
from datetime import timed<PERSON><PERSON>, datetime
import boto3, botocore
import botocore.config
import uuid, mimetypes
from botocore.exceptions import ClientError
import serverless_wsgi
import json
# Create Flask app
app = Flask(__name__)

# Load environment variables from .env file
load_dotenv()

# DynamoDB Config
REGION = os.getenv("REGION", 'ap-southeast-1')
ENDPOINT = os.getenv("ENDPOINT", 'http://localhost:4566')
session = boto3.session.Session(
    aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID",'test'),
    aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY", 'test'),
    region_name=REGION,
)
dynamodb = session.resource("dynamodb", endpoint_url=ENDPOINT)
todo_table = dynamodb.Table("todo")

# S3 Config
S3_ENDPOINT = os.getenv("S3_ENDPOINT", "http://localhost:4566")
S3_BUCKET   = os.getenv("S3_BUCKET", "todo-assets")

session = boto3.session.Session(
    aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID","test"),
    aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY","test"),
    region_name=REGION,
)

s3 = session.client("s3", endpoint_url=S3_ENDPOINT, config=botocore.config.Config(signature_version="s3v4"))
# putfile function
def put_fileobj(fileobj, key, content_type: str):
    s3.upload_fileobj(
        Fileobj=fileobj,
        Bucket=S3_BUCKET,
        Key=key,
        ExtraArgs={"ContentType": content_type, "ACL": "public-read"}  # ACL tuỳ nhu cầu
    )
    return f"{S3_ENDPOINT}/{S3_BUCKET}/{key}"

def gen_key(task_id: str, filename: str):
    # giữ phần mở rộng cho đúng Content-Type
    ext = (filename.rsplit(".",1)[-1].lower() if "." in filename else None)
    if not ext:
        # đoán bằng mimetypes
        ext = (mimetypes.guess_extension(mimetypes.guess_type(filename)[0] or "") or "").lstrip(".") or "bin"
    return f"tasks/{task_id}/{uuid.uuid4().hex}.{ext}"

def user_pk(uid):
    return f"USER#{uid}"

def task_pk(user_id):
    return f"USER#{user_id}"

def task_sk(task_id):
    return f"TASK#{task_id}"

def image_pk(task_id):
    return f"TASK#{task_id}"

def image_sk(image_id):
    return f"IMAGE#{image_id}"

@app.post("/tasks/<task_id>/attachments")
@jwt_required()
def upload_task_image(task_id):
    userId = get_jwt_identity()
    
    # Validate task exists
    try:
        task_response = todo_table.get_item(Key={
            "PK": task_pk(userId),
            "SK": task_sk(task_id)
        })
        if "Item" not in task_response:
            return jsonify(msg="Task not found"), 404
        task = task_response["Item"]
    except Exception:
        return jsonify(msg="Task not found"), 404
    
    # Check if task already has 2 images
    current_images = task.get('images', [])
    if len(current_images) >= 2:
        return jsonify(msg="Maximum 2 images allowed per task"), 400
    
    if "file" not in request.files:
        return jsonify(msg="Missing file"), 400
    
    f = request.files["file"]
    if not f.filename:
        return jsonify(msg="Empty filename"), 400
    
    if f.mimetype not in {"image/jpeg","image/png","image/webp","image/gif"}:
        return jsonify(msg="Only image accepted", mimetype=f.mimetype), 415
    
    try:
        key = gen_key(task_id, f.filename)
        url = put_fileobj(f.stream, key, f.mimetype)
        
        # Create new image object
        new_image = {
            "imageId": key,
            "createdAt": datetime.now().isoformat(),
        }
        
        # Update task with new image
        current_images = task.get('images', [])
        current_images.append(new_image)
        
        todo_table.update_item(
            Key={
                "PK": task_pk(userId),
                "SK": task_sk(task_id)
            },
            UpdateExpression="SET images = :images",
            ExpressionAttributeValues={
                ":images": current_images
            }
        )
        
        return jsonify(msg="Added image", data={"imageId": key, "url": url})
    except Exception as e:
        return jsonify(msg="Upload failed, please try again", error=str(e)), 500

@app.delete("/tasks/<task_id>/attachments")
@jwt_required()
def delete_task_image(task_id):
    userId = get_jwt_identity()
    image_id = request.args.get("imageId")
    
    # Validate task exists
    try:
        task_response = todo_table.get_item(Key={
            "PK": task_pk(userId),
            "SK": task_sk(task_id)
        })
        if "Item" not in task_response:
            return jsonify(msg="Task not found"), 404
        task = task_response["Item"]
    except Exception:
        return jsonify(msg="Task not found"), 404
    try:
        # Remove image from array
        current_images = task.get('images', [])
        updated_images = [img for img in current_images if img['imageId'] != image_id]
        
        if len(updated_images) == len(current_images):
            return jsonify(msg="Image not found"), 404
        
        # Delete from S3
        s3.delete_object(Bucket=S3_BUCKET, Key=image_id)
        
        # Update task with filtered images
        todo_table.update_item(
            Key={
                "PK": task_pk(userId),
                "SK": task_sk(task_id)
            },
            UpdateExpression="SET images = :images",
            ExpressionAttributeValues={
                ":images": updated_images
            }
        )
        
        return jsonify(msg="Deleted image")
    except Exception as e:
        return jsonify(msg="Error deleting image", error=str(e)), 500

@app.get("/image")
def get_image():
    image_id = request.args.get("id")
    try:
        url = s3.generate_presigned_url(
            ClientMethod="get_object",
            Params={"Bucket": S3_BUCKET, "Key": image_id},
            ExpiresIn=3600,
        )
        return redirect(url)
    except ClientError as e:
        return jsonify(error="Image not found"), 404    
    
class Config:
    SECRET_KEY = os.getenv("SECRET_KEY", "super-secret-key")    
    JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "super-secret-key")
    FLASK_ENV = os.getenv("FLASK_ENV", "production")
    GOOGLE_CLIENT_ID = "402661775941-254t4ga9gckbqvibmvriuhhms23cha4q.apps.googleusercontent.com"
    GOOGLE_CLIENT_SECRET = "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"
    GOOGLE_REDIRECT_URI = os.getenv("GOOGLE_REDIRECT_URI", "http://localhost/api/auth/google/login")
    POST_LOGIN_REDIRECT= os.getenv("POST_LOGIN_REDIRECT")
    POST_LOGIN_REDIRECT= 'http://localhost/api/auth/google/callback'
    LOCALSTACK_URL = os.getenv("LOCALSTACK_URL", "http://localhost:4566")
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(minutes=15)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    JWT_TOKEN_LOCATION = os.getenv("JWT_TOKEN_LOCATION", ["headers", "cookies"])
    JWT_COOKIE_SECURE = os.getenv("JWT_COOKIE_SECURE", False)
    JWT_COOKIE_SAMESITE = os.getenv("JWT_COOKIE_SAMESITE", "Lax")
    JWT_COOKIE_CSRF_PROTECT = False


# Configure app
app.config.from_object(Config)
CORS(app, supports_credentials=True)

#JWT
jwt = JWTManager(app)

oauth = OAuth(app)
google = oauth.register(
    name="google",
    server_metadata_url="https://accounts.google.com/.well-known/openid-configuration",
    client_id=app.config["GOOGLE_CLIENT_ID"],
    client_secret=app.config["GOOGLE_CLIENT_SECRET"],
    access_token_url="https://oauth2.googleapis.com/token",
    authorize_url="https://accounts.google.com/o/oauth2/v2/auth",
    api_base_url="https://www.googleapis.com/oauth2/v1/",
    userinfo_endpoint="https://openidconnect.googleapis.com/v1/userinfo",
    client_kwargs={"scope": "openid email profile"},
)

@app.get("/health")
def health():
    return jsonify(status='ok')


@app.get("/auth/google/login")
def login_with_google():
    # Lấy redirect_uri từ query parameter (FE sẽ truyền vào)
    redirect_uri = request.args.get("redirect_uri", app.config["POST_LOGIN_REDIRECT"])

    # Tạo state để bảo mật OAuth flow
    import secrets
    state = secrets.token_urlsafe(32)

    # Lưu state và redirect_uri vào session
    from flask import session
    session['oauth_state'] = state
    session['oauth_redirect_uri'] = redirect_uri

    # Redirect đến Google OAuth với state
    return google.authorize_redirect(redirect_uri, state=state)


@app.post("/auth/google/process")
def process_oauth_callback():
    try:
        data = request.get_json()
        state = data.get('state')
        code = data.get('code')

        if not state or not code:
            return jsonify({"msg": "Missing state or code parameter"}), 400

        # Xác thực state để bảo mật
        from flask import session
        stored_state = session.get('oauth_state')
        stored_redirect_uri = session.get('oauth_redirect_uri')

        if not stored_state or stored_state != state:
            return jsonify({"msg": "Invalid state parameter"}), 400

        if not stored_redirect_uri:
            return jsonify({"msg": "Missing redirect_uri in session"}), 400

        # Xóa state và redirect_uri khỏi session sau khi sử dụng
        session.pop('oauth_state', None)
        session.pop('oauth_redirect_uri', None)

        # Sử dụng redirect_uri từ session để đảm bảo consistency
        redirect_uri = stored_redirect_uri

        token_data = {
            'code': code,
            'client_id': app.config["GOOGLE_CLIENT_ID"],
            'client_secret': app.config["GOOGLE_CLIENT_SECRET"],
            'redirect_uri': redirect_uri,
            'grant_type': 'authorization_code'
        }

        # Gọi Google để lấy access token
        import requests
        token_response = requests.post('https://oauth2.googleapis.com/token', data=token_data)
        token_result = token_response.json()

        if 'access_token' not in token_result:
            return jsonify({"msg": "Failed to get access token from Google"}), 400

        # Lấy user info từ Google
        userinfo_response = requests.get(
            'https://www.googleapis.com/oauth2/v1/userinfo',
            headers={'Authorization': f'Bearer {token_result["access_token"]}'}
        )
        user_info = userinfo_response.json()

        if not user_info:
            return jsonify({"msg": "Failed to get user info from Google"}), 400

        uid = user_info.get("sub") or user_info.get("id")

        # Ensure user exists in DB
        ensure_user(user_info)

        # Tạo JWT tokens
        jwt_access = create_access_token(identity=uid, additional_claims={
            "email": user_info.get("email"),
            "name": user_info.get("name"),
        })
        jwt_refresh = create_refresh_token(identity=uid)

        # Trả về JSON response với tokens và user info
        return jsonify({
            "msg": "Login successful",
            "data": {
                "access_token": jwt_access,
                "refresh_token": jwt_refresh,
                "user": {
                    "userId": uid,
                    "email": user_info.get("email"),
                    "name": user_info.get("name")
                }
            }
        })

    except Exception as e:
        return jsonify({"msg": "Something went wrong", "error": str(e)}), 500

def ensure_user(userInfo: dict) -> str:
    uid = userInfo.get("sub") or userInfo.get("id")
    
    try:
        # Check if user exists
        response = todo_table.get_item(Key={"PK": user_pk(uid), "SK": "USER#INFO"})
        if "Item" in response:
            return uid  # User exists, return uid
    except Exception:
        pass
    
    # User doesn't exist, create new user
    try: 
        todo_table.put_item(Item={
            "PK": user_pk(uid),
            "SK": "USER#INFO",
            "email": userInfo.get("email"),
            "name": userInfo.get("name"),
            "createdAt": datetime.now().isoformat(),
        })
        return uid
    except Exception as e:
        raise Exception(f"Failed to create user: {str(e)}")

@app.get("/me")
@jwt_required()
def me():
    userId = get_jwt_identity() 
    try:
        response = todo_table.get_item(Key={"PK": user_pk(userId), "SK": "USER#INFO"})
        if "Item" not in response:
            return {"msg": "User not found"}, 404
        current_user = response["Item"]
        return {"data": current_user}
    except Exception as e:
        return {"msg": "Error fetching user", "error": str(e)}, 500

@app.post("/logout")
@jwt_required()
def logout():
    # detroy token
    resp = make_response(redirect('/'))
    unset_jwt_cookies(resp)
    return resp, 200


@app.post("/auth/refresh")
def auth_refresh():
    """Alternative refresh endpoint that accepts refresh_token in request body"""
    try:
        data = request.get_json()
        refresh_token = data.get('refresh_token')

        if not refresh_token:
            return jsonify({"msg": "Missing refresh_token"}), 400

        # Verify refresh token manually
        from flask_jwt_extended import decode_token
        try:
            decoded_token = decode_token(refresh_token)
            if decoded_token['type'] != 'refresh':
                return jsonify({"msg": "Invalid token type"}), 400

            userId = decoded_token['sub']

            # Create new tokens
            jwt_access = create_access_token(identity=userId)
            jwt_refresh = create_refresh_token(identity=userId)

            return jsonify({
                "msg": "Token refreshed successfully",
                "data": {
                    "access_token": jwt_access,
                    "refresh_token": jwt_refresh
                }
            })

        except Exception as e:
            return jsonify({"msg": "Invalid refresh token", "error": str(e)}), 401

    except Exception as e:
        return jsonify({"msg": "Something went wrong", "error": str(e)}), 500


def ensure_user(userInfo: dict) -> str:
    uid = userInfo.get("sub") or userInfo.get("id")
    
    try:
        # Check if user exists
        response = todo_table.get_item(Key={"PK": user_pk(uid), "SK": "USER#INFO"})
        if "Item" in response:
            return uid  # User exists, return uid
    except Exception:
        pass
    
    # User doesn't exist, create new user
    try: 
        todo_table.put_item(Item={
            "PK": user_pk(uid),
            "SK": "USER#INFO",
            "email": userInfo.get("email"),
            "name": userInfo.get("name"),
            "createdAt": datetime.now().isoformat(),
        })
        return uid
    except Exception as e:
        raise Exception(f"Failed to create user: {str(e)}")

def create_user(userInfo: dict) -> dict:
    uid = userInfo.get("sub") or userInfo.get("id")
    try: 
        todo_table.put_item(Item={
            "PK": user_pk(uid),
            "SK": "USER#INFO",
            "email": userInfo.get("email"),
            "name": userInfo.get("name"),
        })
    except Exception as e:
        return {"msg": "Something went wrong", "error": str(e)}
    return {"msg": "Nice to meet you!"}

## Init Table
def ensure_table():
    TABLE_NAME = os.getenv("DDB_TABLE", "todo")
    client = session.client("dynamodb", endpoint_url=ENDPOINT or None,
                            config=botocore.config.Config(signature_version="v4"))
    try:
        client.describe_table(TableName=TABLE_NAME)
        return  # đã có
    except client.exceptions.ResourceNotFoundException:
        pass

    client.create_table(
        TableName=TABLE_NAME,
        BillingMode="PAY_PER_REQUEST",
        AttributeDefinitions=[
            {"AttributeName": "PK", "AttributeType": "S"},
            {"AttributeName": "SK", "AttributeType": "S"},
            {"AttributeName": "GSI1PK", "AttributeType": "S"},
            {"AttributeName": "GSI1SK", "AttributeType": "S"},
        ],
        KeySchema=[
            {"AttributeName": "PK", "KeyType": "HASH"},
            {"AttributeName": "SK", "KeyType": "RANGE"},
        ],
        GlobalSecondaryIndexes=[
            {
                "IndexName": "GSI1",
                "KeySchema": [
                    {"AttributeName": "GSI1PK", "KeyType": "HASH"},
                    {"AttributeName": "GSI1SK", "KeyType": "RANGE"},
                ],
                "Projection": {"ProjectionType": "ALL"},
            }
        ],
    )
    waiter = client.get_waiter("table_exists")
    waiter.wait(TableName=TABLE_NAME)

try:
    ensure_table()
except Exception as e:
    print(f"Error during table creation: {e}")
    import traceback
    traceback.print_exc()
    import sys
    sys.exit(1)

def create_bucket_if_not_exists(s3_client, bucket_name):
    try:
        s3_client.head_bucket(Bucket=bucket_name)
        print(f"Bucket '{bucket_name}' already exists.")
    except ClientError as e:
        # Nếu lỗi là 404 (Not Found), thì bucket chưa tồn tại
        if e.response['Error']['Code'] == '404':
            print(f"S3 Bucket '{bucket_name}' not found. Creating it...")
            s3_client.create_bucket(Bucket=bucket_name, CreateBucketConfiguration={'LocationConstraint': REGION})
            print(f"S3 Bucket '{bucket_name}' created successfully.")
        else:
            # Lỗi khác
            print("An error occurred:", e)
            raise

create_bucket_if_not_exists(s3, S3_BUCKET)
@app.get("/tasks")
@jwt_required()
def get_tasks():
    userId = get_jwt_identity()
    try:
        # Query tasks for user using GSI1
        response = todo_table.query(
            IndexName="GSI1",
            KeyConditionExpression="GSI1PK = :pk AND begins_with(GSI1SK, :sk)",
            ExpressionAttributeValues={
                ":pk": f"USER#{userId}",
                ":sk": "TASK#"
            },
            ScanIndexForward=False
        )
        tasks = response["Items"]
        
        # Add image URLs for each task
        for task in tasks:
            if 'images' not in task:
                task['images'] = []
            else:
                # Add URLs to existing images
                for image in task['images']:
                    image['url'] = f"{Config.LOCALSTACK_URL}/{S3_BUCKET}/{image['imageId']}"
        
        return {"msg": "", "data": tasks}
    except Exception as e:
        return {"msg": "Error fetching tasks", "error": str(e)}, 500

@app.post("/tasks")
@jwt_required()
def create_task():
    userId = get_jwt_identity()
    data = request.get_json()
    taskId = str(uuid.uuid4())
    created_at = datetime.utcnow().isoformat()
    
    try:
        todo_table.put_item(Item={
            "PK": task_pk(userId),
            "SK": task_sk(taskId),
            "GSI1PK": f"USER#{userId}",
            "GSI1SK": f"TASK#{created_at}#{taskId}",
            "taskId": taskId,
            "userId": userId,
            "title": data.get("title"),
            "description": data.get("description"),
            "completed": False,
            "createdAt": created_at,
            "images": []  # Initialize empty images array
        })
        return {"msg": "Created task", "data": {
            "taskId": taskId,
            "title": data.get("title"),
            "description": data.get("description"),
            "completed": False,
            "createdAt": created_at,
            "images": []
        }}
    except Exception as e:
        return {"msg": "Error creating task", "error": str(e)}, 500

@app.put("/tasks/<taskId>")
@jwt_required()
def update_task(taskId):
    userId = get_jwt_identity()
    data = request.get_json()

    if not data:
        return {"msg": "Request body cannot be empty"}, 400

    update_expressions = []
    expr_attr_names = {}
    expr_attr_values = {}
    
    updatable_fields = ["title", "description", "completed"]
    for field in updatable_fields:
        if field in data:
            placeholder_name = f"#{field}"
            placeholder_value = f":{field}"

            update_expressions.append(f"{placeholder_name} = {placeholder_value}")
            expr_attr_names[placeholder_name] = field
            expr_attr_values[placeholder_value] = data[field]

    if not update_expressions:
        return {"msg": "No fields to update"}, 400

    # Luôn cập nhật trường 'updatedAt'
    update_expressions.append("#updatedAt = :updatedAt")
    expr_attr_names["#updatedAt"] = "updatedAt"
    expr_attr_values[":updatedAt"] = datetime.utcnow().isoformat()

    update_expression_str = "SET " + ", ".join(update_expressions)

    try:
        response = todo_table.update_item(
            Key={
                "PK": task_pk(userId),
                "SK": task_sk(taskId)
            },
            UpdateExpression=update_expression_str,
            ExpressionAttributeNames=expr_attr_names,
            ExpressionAttributeValues=expr_attr_values,
            ConditionExpression="attribute_exists(PK)",
            ReturnValues="ALL_NEW"
        )
        return {"msg": "Updated task", "data": response.get('Attributes', {})}

    except ClientError as e:
        if e.response['Error']['Code'] == 'ConditionalCheckFailedException':
            return {"msg": "Task not found"}, 404
        else:
            return {"msg": "An error occurred", "error": str(e)}, 500

@app.delete("/tasks/<taskId>")
@jwt_required()
def delete_task(taskId):
    userId = get_jwt_identity()
    
    # Check if task exists and get images
    try:
        task_response = todo_table.get_item(Key={
            "PK": task_pk(userId),
            "SK": task_sk(taskId)
        })
        if "Item" not in task_response:
            return {"msg": "Task not found"}, 404
        task = task_response["Item"]
    except Exception:
        return {"msg": "Task not found"}, 404
    
    try:
        # Delete all images from S3
        images = task.get('images', [])
        for image in images:
            s3.delete_object(Bucket=S3_BUCKET, Key=image["imageId"])
        
        # Delete the task itself
        todo_table.delete_item(Key={
            "PK": task_pk(userId),
            "SK": task_sk(taskId)
        })
        
        return {"msg": "Deleted task", "data": {"taskId": taskId}}
    except Exception as e:
        return {"msg": "Something went wrong", "error": str(e)}, 500

def lambda_handler(event, context):
    """
    AWS Lambda entry point
    
    Process:
    1. API Gateway sends HTTP request as event
    2. serverless_wsgi converts event to WSGI environ
    3. Flask app processes the request
    4. Response converted back to API Gateway format
    
    Event structure:
    {
        "httpMethod": "GET|POST|PUT|DELETE",
        "path": "/tasks",
        "headers": {...},
        "body": "...",
        "queryStringParameters": {...}
    }
    """
    return serverless_wsgi.handle_request(app, event, context)

# For local development
if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=3001)
