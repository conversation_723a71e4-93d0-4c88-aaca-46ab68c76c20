import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { user as User } from '@/app/types/types'; // Định nghĩa type cho User
import apiClient from '@/app/services/apiClient'; // API client với interceptor

// <PERSON><PERSON><PERSON> nghĩa interface cho tokens
interface AuthTokens {
  access_token: string;
  refresh_token: string;
}

// Định nghĩa trạng thái và các hành động của store
interface AuthState {
  user: User | null;
  tokens: AuthTokens | null;
  isLoading: boolean;
  hasAttempted: boolean; // Đ<PERSON><PERSON> dấu đã thử fetch ít nhất 1 lần
  // Hàm chính để lấy thông tin người dùng
  fetchCurrentUser: () => Promise<void>;
  // Hàm để logout
  logout: () => Promise<void>;
  // Hàm reset state khi cần thiết
  reset: () => void;
  // Hàm cho phép thử lại fetch
  allowRetry: () => void;
  // Hàm xử lý OAuth callback
  processOAuthCallback: (state: string, code: string) => Promise<void>;
  // Hàm lưu tokens
  setTokens: (tokens: AuthTokens) => void;
  // Hàm lấy access token
  getAccessToken: () => string | null;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      tokens: null,
      isLoading: false,
      hasAttempted: false,

  // Hành động: Lấy thông tin người dùng hiện tại
  fetchCurrentUser: async () => {
    const { user, isLoading, hasAttempted } = useAuthStore.getState();

    // Tránh gọi API nếu:
    // 1. Đã có user (quan trọng nhất - đã có data rồi thì không cần gọi lại)
    // 2. Đang loading
    // 3. Đã thử fetch rồi (tránh vòng lặp)
    if (user || isLoading || hasAttempted) {
      console.log('Skip fetchCurrentUser:', {
        hasUser: !!user,
        isLoading,
        hasAttempted
      });
      return;
    }

    console.log('Starting fetchCurrentUser...');
    set({ isLoading: true });
    try {
      // Gọi API /me
      const response = await apiClient.get('/me');
      const currentUser = response.data.data; // Lấy data từ response structure
      // Nếu thành công, lưu thông tin user vào store
      console.log('fetchCurrentUser success:', currentUser);
      set({ user: currentUser, isLoading: false, hasAttempted: true });
    } catch (error) {
      // Nếu thất bại (token không hợp lệ...), set user là null
      console.error('Failed to fetch current user:', error);
      set({ user: null, isLoading: false, hasAttempted: true });
    }
  },

  // Hành động: Đăng xuất
  logout: async () => {
    try {
      // Gọi API logout để xóa cookie trên server
      await apiClient.post('/logout');
    } catch (error) {
      console.error('Failed to logout on server:', error);
      // Vẫn tiếp tục logout phía client dù server có lỗi
    } finally {
      // Xóa thông tin người dùng khỏi store và reset hasAttempted
      set({ user: null, isLoading: false, hasAttempted: false });
    }
  },

  // Hàm reset state - hữu ích khi cần reset lại trạng thái
  reset: () => {
    console.log('Resetting auth store...');
    set({ user: null, isLoading: false, hasAttempted: false });
  },

  // Hàm để reset hasAttempted khi cần fetch lại (ví dụ sau khi đăng nhập)
  allowRetry: () => {
    const { hasAttempted } = useAuthStore.getState();
    if (hasAttempted) {
      console.log('Allowing retry for fetchCurrentUser...');
      set({ hasAttempted: false });
    }
  },

  // Hàm xử lý OAuth callback
  processOAuthCallback: async (state: string, code: string) => {
    set({ isLoading: true });
    try {
      console.log('Processing OAuth callback with state:', state, 'code:', code);

      // Gọi API BE để xử lý callback và lấy tokens
      const response = await apiClient.post('/auth/google/callback', {
        state,
        code
      });

      const { access_token, refresh_token, user } = response.data.data;

      if (!access_token || !refresh_token) {
        throw new Error('Missing tokens in response');
      }

      // Lưu tokens và user info vào store
      set({
        tokens: { access_token, refresh_token },
        user: user,
        isLoading: false,
        hasAttempted: true
      });

      console.log('OAuth callback processed successfully');
    } catch (error) {
      console.error('Failed to process OAuth callback:', error);
      set({
        tokens: null,
        user: null,
        isLoading: false,
        hasAttempted: true
      });
      throw error;
    }
  },

  // Hàm lưu tokens
  setTokens: (tokens: AuthTokens) => {
    set({ tokens });
  },

  // Hàm lấy access token
  getAccessToken: () => {
    const { tokens } = get();
    return tokens?.access_token || null;
  },
    }),
    {
      name: 'auth-storage', // tên key trong localStorage
      partialize: (state) => ({
        tokens: state.tokens,
        user: state.user
      }), // chỉ persist tokens và user, không persist loading states
    }
  )
);
