'use client';

import { useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';

export function AuthInitializer() {
  const pathname = usePathname();
  const router = useRouter();
  const { user, tokens, fetchCurrentUser, reset, hasAttempted, isLoading, getAccessToken } = useAuthStore();

  // Danh sách các route public không cần gọi API /me
  const publicRoutes = ['/login', '/auth/process'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  useEffect(() => {
    // Nếu chuyển sang public route, reset state để chuẩn bị cho lần đăng nhập tiếp theo
    if (isPublicRoute) {
      // Nếu đã có tokens và đang ở login page, redirect về tasks
      if (tokens && pathname.startsWith('/login')) {
        router.push('/tasks');
        return;
      }
      // Không reset state khi ở /auth/process vì đang xử lý OAuth
      if (!pathname.startsWith('/auth/process')) {
        reset();
      }
      return;
    }

    // Kiểm tra xem có tokens không
    const accessToken = getAccessToken();

    // Nếu không có tokens và không phải public route, redirect về login
    if (!accessToken && !isPublicRoute) {
      console.log('No access token found, redirecting to login...');
      router.push('/login');
      return;
    }

    // Chỉ gọi API nếu:
    // 1. Không phải public route
    // 2. Có tokens
    // 3. Chưa có user trong store
    // 4. Chưa từng thử fetch (hasAttempted = false)
    // 5. Không đang loading
    if (accessToken && !user && !hasAttempted && !isLoading) {
      console.log('Access token found but no user in store, fetching from API...');
      fetchCurrentUser();
    }
  }, [user, tokens, fetchCurrentUser, reset, isPublicRoute, hasAttempted, isLoading, getAccessToken, pathname, router]);

  return null;
}