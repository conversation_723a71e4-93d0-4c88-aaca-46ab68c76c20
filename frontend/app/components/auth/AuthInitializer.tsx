'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';

export function AuthInitializer() {
  const pathname = usePathname();
  const { user, fetchCurrentUser, reset, hasAttempted, isLoading } = useAuthStore();

  // Danh sách các route public không cần gọi API /me
  const publicRoutes = ['/login', '/auth/process'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  useEffect(() => {
    // N<PERSON><PERSON> chuyển sang public route, reset state để chuẩn bị cho lần đăng nhập tiếp theo
    if (isPublicRoute) {
      reset();
      return;
    }

    // Chỉ gọi API nếu:
    // 1. Không phải public route
    // 2. Chưa có user trong store
    // 3. <PERSON><PERSON>a từng thử fetch (hasAttempted = false)
    // 4. Không đang loading
    if (!user && !hasAttempted && !isLoading) {
      console.log('User not found in store, fetching from API...');
      fetchCurrentUser();
    }
  }, [user, fetchCurrentUser, reset, isPublicRoute, hasAttempted, isLoading]);

  return null;
}