"use client";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";

export default function LoginPage() {
  const handleGoogleLogin = () => {
    // Tạo redirect_uri về trang process của FE

    // Tạo URL đến BE với redirect_uri
    const backendUrl = process.env.NEXT_PUBLIC_API_URL;
    const googleAuthUrl = `${backendUrl}auth/google/login`;

    console.log('Redirecting to:', googleAuthUrl);

    // Redirect đến BE để bắt đầu OAuth flow
    window.location.href = googleAuthUrl;
  };

  return (
    <main className="min-h-dvh flex items-center justify-center p-6 bg-muted/30">
      <Card className="w-full max-w-sm">
        <CardHeader>
          <CardTitle className="text-center">Welcome</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-center text-sm text-muted-foreground">
          <p>Login to Todo App.</p>
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleGoogleLogin}
            className="w-full"
            variant="default"
          >
            <GoogleIcon className="mr-2 h-5 w-5" />
            Continue with Google
          </Button>
        </CardFooter>
      </Card>
    </main>
  );
}

function GoogleIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} viewBox="0 0 24 24" aria-hidden="true">
      <path fill="#EA4335" d="M12 11.999h10.5c.1.6.15 1.2.15 1.9 0 5.5-3.7 9.4-10.65 9.4A11.85 11.85 0 0 1 0 12 12 12 0 0 1 12 0c3.2 0 5.9 1.2 7.95 3.1l-3.2 3.1C15.5 4.8 13.9 4.2 12 4.2 7.9 4.2 4.7 7.5 4.7 11.6s3.2 7.4 7.3 7.4c4.6 0 6.3-3.2 6.6-4.9H12v-2.1Z" />
    </svg>
  );
}
