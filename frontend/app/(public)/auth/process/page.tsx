'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

export default function AuthProcessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { processOAuthCallback } = useAuthStore();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    const processAuth = async () => {
      try {
        // Lấy state và code từ URL parameters
        const state = searchParams.get('state');
        const code = searchParams.get('code');
        const error = searchParams.get('error');

        // <PERSON><PERSON><PERSON> tra nếu có lỗi từ Google OAuth
        if (error) {
          setStatus('error');
          setErrorMessage(`OAuth error: ${error}`);
          return;
        }

        // Kiểm tra các parameters cần thiết
        if (!state || !code) {
          setStatus('error');
          setErrorMessage('Missing required parameters (state or code)');
          return;
        }

        console.log('Processing OAuth callback with state:', state, 'and code:', code);

        // Gọi function trong AuthStore để xử lý callback
        await processOAuthCallback(state, code);

        setStatus('success');
        
        // Redirect về trang tasks sau 1 giây
        setTimeout(() => {
          router.push('/tasks');
        }, 1000);

      } catch (error) {
        console.error('Error processing OAuth callback:', error);
        setStatus('error');
        setErrorMessage(error instanceof Error ? error.message : 'Unknown error occurred');
      }
    };

    processAuth();
  }, [searchParams, processOAuthCallback, router]);

  const renderContent = () => {
    switch (status) {
      case 'processing':
        return (
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin" />
            <p className="text-sm text-muted-foreground">Processing your login...</p>
          </div>
        );
      
      case 'success':
        return (
          <div className="flex flex-col items-center space-y-4">
            <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
              <svg className="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="text-sm text-muted-foreground">Login successful! Redirecting...</p>
          </div>
        );
      
      case 'error':
        return (
          <div className="flex flex-col items-center space-y-4">
            <div className="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center">
              <svg className="h-5 w-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <p className="text-sm text-red-600">{errorMessage}</p>
            <button 
              onClick={() => router.push('/login')}
              className="text-sm text-blue-600 hover:underline"
            >
              Try again
            </button>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <main className="min-h-dvh flex items-center justify-center p-6 bg-muted/30">
      <Card className="w-full max-w-sm">
        <CardHeader>
          <CardTitle className="text-center">Authentication</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-center">
          {renderContent()}
        </CardContent>
      </Card>
    </main>
  );
}
