import { NextResponse } from "next/server";
import { NextRequest } from "next/server";

export function middleware(req: NextRequest) {
    const { pathname } = req.nextUrl;
    const publicRoutes = ["/login", "/auth/process"];

    // Kiểm tra xem có auth-storage trong localStorage không
    // Vì middleware chạy server-side, chúng ta sẽ kiểm tra cookies hoặc
    // để client-side AuthInitializer xử lý authentication logic
    const accessTokenCookie = req.cookies.get('access_token_cookie');
    const refreshTokenCookie = req.cookies.get('refresh_token_cookie');

    // Kiểm tra có bất kỳ token nào không (cookies hoặc sẽ có trong localStorage)
    const hasTokens = accessTokenCookie || refreshTokenCookie;

    // Nếu đang ở route public
    if (publicRoutes.some((route) => pathname.startsWith(route))) {
        // Nếu có tokens và đang ở login page, redirect về tasks
        if (hasTokens && pathname.startsWith('/login')) {
            return NextResponse.redirect(new URL("/tasks", req.url));
        }
        // Cho phép truy cập các route public khác
        return NextResponse.next();
    }

    // Nếu ở trang chủ và có tokens, redirect về tasks
    if ((pathname === "/" || pathname === "") && hasTokens) {
        return NextResponse.redirect(new URL("/tasks", req.url));
    }

    // Nếu ở trang chủ và không có tokens, redirect về login
    if ((pathname === "/" || pathname === "") && !hasTokens) {
        return NextResponse.redirect(new URL("/login", req.url));
    }

    // Đối với các route protected khác, để AuthInitializer xử lý
    // vì nó có thể kiểm tra localStorage và Zustand store
    return NextResponse.next();
}

// Chỉ áp dụng middleware cho các route cần thiết
export const config = {
    matcher: [
        "/((?!api|_next/static|_next/image|favicon.ico).*)",
        "/tasks"
    ]
};