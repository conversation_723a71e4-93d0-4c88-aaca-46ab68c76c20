export interface ApiResponse<T> {
  msg?: string;
  statusCode?: number;
  data?: T;
}

export interface image {
  taskId?: string;
  imageId: string;
  createdAt?: string;
  url?: string
}
export interface task {
  taskId: string;
  userId?: string;
  title?: string;
  description?: string;
  createdAt?: string;
  completed?: boolean;
  updatedAt?: string;
  images?: image[];
}

export interface user {
  userId: string;
  name?: string;
  email?: string;
  tasks?: task[];
}