import axios from 'axios';
import { toast } from 'sonner';
import envConf from '@/app/config/config';
import { useAuthStore } from '@/stores/authStore';
// -----------------------------------------------------------------------------
// CẤU HÌNH AXIOS INSTANCE
// -----------------------------------------------------------------------------

const apiClient = axios.create({
  // Lấy URL của backend từ config
  baseURL: envConf.NEXT_PUBLIC_API_ENDPOINT,
  // Luôn gửi cookie trong mỗi request
  withCredentials: true,
});

// -----------------------------------------------------------------------------
// AXIOS INTERCEPTOR
// -----------------------------------------------------------------------------

// Request interceptor để thêm access token vào headers
apiClient.interceptors.request.use(
  (config) => {
    // Lấy access token từ store
    const accessToken = useAuthStore.getState().getAccessToken();

    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Biến để theo dõi trạng thái refresh token
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// Interceptor này sẽ "chặn" mọi response trả về
apiClient.interceptors.response.use(
  // 1. Hàm xử lý khi response thành công (status 2xx)
  (response) => {
    if (response.data.msg) toast(response.data.msg);
    return response;
  },

  // 2. Hàm xử lý khi response bị lỗi (status ngoài 2xx)
  async (error) => {
    const originalRequest = error.config;

    // Kiểm tra xem lỗi có phải là 401 (Unauthorized) và request này chưa được thử lại
    if (error.response?.status === 401 && !originalRequest._retry) {
      // Nếu đang ở trang login, không cần thử refresh
      if (typeof window !== 'undefined' && window.location.pathname === '/login') {
        toast('Your session has expired. Please log in again.');
        return Promise.reject(error);
      }

      if (isRefreshing) {
        // Nếu đang refresh, đưa request vào queue
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(() => {
          return apiClient(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        console.log('Access token expired. Attempting to refresh...');

        // Lấy refresh token từ store
        const { tokens } = useAuthStore.getState();
        if (!tokens?.refresh_token) {
          throw new Error('No refresh token available');
        }

        // Gửi request để làm mới token với refresh token
        const refreshResponse = await apiClient.post('auth/refresh', {
          refresh_token: tokens.refresh_token
        });

        const { access_token, refresh_token } = refreshResponse.data.data;

        // Cập nhật tokens mới vào store
        useAuthStore.getState().setTokens({
          access_token,
          refresh_token
        });

        console.log('Token refreshed successfully. Processing queued requests...');
        processQueue(null, access_token);

        // Sau khi refresh thành công, gọi lại request ban đầu
        return apiClient(originalRequest);
      } catch (refreshError) {
        console.error('Failed to refresh token:', refreshError);
        processQueue(refreshError, null);

        // Clear tokens và user từ store
        useAuthStore.getState().reset();

        // Chỉ redirect nếu không ở trang login
        if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
          toast('Your session has expired. Please log in again.');
          window.location.href = '/login';
        }
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    // Nếu lỗi không phải 401 hoặc đã thử lại rồi, trả về lỗi ban đầu
    return Promise.reject(error);
  }
);

export default apiClient;