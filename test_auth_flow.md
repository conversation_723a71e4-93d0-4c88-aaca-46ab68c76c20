# Test Auth Flow

## Flow đã được implement:

### 1. Frontend Login Page (`/login`)
- User click "Continue with Google"
- FE tạo `redirect_uri = ${frontendUrl}/auth/process`
- FE redirect đến `${backendUrl}/auth/google/login?redirect_uri=${redirect_uri}`

### 2. Backend Login Endpoint (`/auth/google/login`)
- <PERSON><PERSON><PERSON>n `redirect_uri` từ query parameter
- Tạo random `state` để bảo mật
- Lưu `state` vào Flask session
- Redirect đến Google OAuth với `redirect_uri` và `state`

### 3. Google OAuth
- User đăng nhập với Google
- Google redirect về `${frontendUrl}/auth/process?state=...&code=...`

### 4. Frontend Process Page (`/auth/process`)
- Nhận `state` và `code` từ URL parameters
- Gọi `processOAuthCallback(state, code)` từ AuthStore
- AuthStore gọi `POST /auth/google/process` với `{state, code}`

### 5. Backend Process Endpoint (`/auth/google/process`)
- Xác thực `state` với session
- Dùng `code` để exchange access token từ Google
- Lấy user info từ Google
- Tạo JWT access_token và refresh_token
- Trả về JSON: `{access_token, refresh_token, user}`

### 6. Frontend AuthStore
- Lưu tokens vào Zustand store (persist trong localStorage)
- Redirect về `/tasks`

### 7. API Client
- Tự động thêm `Authorization: Bearer ${access_token}` vào headers
- Tự động refresh token khi 401

### 8. Middleware & AuthInitializer
- Kiểm tra tokens trong store
- Redirect phù hợp giữa public/protected routes

## Để test:

1. Start backend: `cd backend && python app.py`
2. Start frontend: `cd frontend && npm run dev`
3. Truy cập `http://localhost:3000/login`
4. Click "Continue with Google"
5. Đăng nhập với Google
6. Kiểm tra redirect về `/tasks` và tokens trong localStorage

## Debug:

- Check browser console logs
- Check backend logs
- Check localStorage key `auth-storage`
- Check network requests trong DevTools
